"use client";

import React, { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { TransactionForm } from "@/components/transactions/TransactionForm";
import { TransactionList } from "@/components/transactions/TransactionList";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { toast } from "sonner";

import { createTransaction, getActiveCompanies, getUsers } from "@/lib/firestore";
import { type Company, type User, type Transaction } from "@/types";
import { type CreateTransactionInput } from "@/lib/validations";

export default function TransactionsPage() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Load companies and users on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const [companiesData, usersData] = await Promise.all([
          getActiveCompanies(),
          getUsers()
        ]);
        
        setCompanies(companiesData);
        setUsers(usersData);
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load companies and users');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleCreateTransaction = async (data: CreateTransactionInput) => {
    try {
      setIsSubmitting(true);
      const transactionId = await createTransaction(data);

      toast.success('Transaction created successfully!');
      setIsDialogOpen(false);

      console.log('Created transaction with ID:', transactionId);
    } catch (error) {
      console.error('Error creating transaction:', error);
      toast.error('Failed to create transaction');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewTransaction = (transaction: Transaction) => {
    // TODO: Navigate to transaction detail page
    console.log('View transaction:', transaction.id);
  };

  const handleEditTransaction = (transaction: Transaction) => {
    // TODO: Open edit dialog or navigate to edit page
    console.log('Edit transaction:', transaction.id);
  };

  const handleDeleteTransaction = (transaction: Transaction) => {
    // TODO: Show confirmation dialog and delete
    console.log('Delete transaction:', transaction.id);
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading...</p>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Transactions</h1>
              <p className="text-gray-600">Manage real estate transactions</p>
            </div>
            
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  New Transaction
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Create New Transaction</DialogTitle>
                  <DialogDescription>
                    Enter the details for a new real estate transaction.
                  </DialogDescription>
                </DialogHeader>
                <TransactionForm
                  companies={companies}
                  users={users}
                  onSubmit={handleCreateTransaction}
                  isSubmitting={isSubmitting}
                  mode="create"
                />
              </DialogContent>
            </Dialog>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  +0% from last month
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  Active transactions
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  This month
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$0</div>
                <p className="text-xs text-muted-foreground">
                  Combined property value
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Transaction List */}
          <TransactionList
            companies={companies}
            users={users}
            onViewTransaction={handleViewTransaction}
            onEditTransaction={handleEditTransaction}
            onDeleteTransaction={handleDeleteTransaction}
          />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
